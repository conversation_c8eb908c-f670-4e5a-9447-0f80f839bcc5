"""
Example usage of the TodoWrite LangChain tool.

This file demonstrates how to use the TodoWrite tool in various scenarios.
"""

from todowrite import todo_write


def example_complex_task():
    """Example: Complex multi-step task requiring todo list management."""
    
    # Initial todo list for implementing dark mode feature
    initial_todos = [
        {
            "content": "Create dark mode toggle component in Settings page",
            "status": "pending",
            "priority": "high",
            "id": "dark-mode-toggle-001"
        },
        {
            "content": "Add dark mode state management (context/store)",
            "status": "pending", 
            "priority": "high",
            "id": "dark-mode-state-002"
        },
        {
            "content": "Implement CSS-in-JS styles for dark theme",
            "status": "pending",
            "priority": "medium",
            "id": "dark-mode-styles-003"
        },
        {
            "content": "Update existing components to support theme switching",
            "status": "pending",
            "priority": "medium", 
            "id": "dark-mode-components-004"
        },
        {
            "content": "Run tests and build process, addressing any failures",
            "status": "pending",
            "priority": "low",
            "id": "dark-mode-testing-005"
        }
    ]
    
    print("=== Initial Todo List ===")
    result = todo_write(initial_todos)
    print(result)
    
    # Update: Start working on first task
    updated_todos = initial_todos.copy()
    updated_todos[0]["status"] = "in_progress"
    
    print("\n=== After Starting First Task ===")
    result = todo_write(updated_todos)
    print(result)
    
    # Update: Complete first task, start second
    updated_todos[0]["status"] = "completed"
    updated_todos[1]["status"] = "in_progress"
    
    print("\n=== After Completing First Task ===")
    result = todo_write(updated_todos)
    print(result)


def example_multiple_features():
    """Example: User provides multiple features to implement."""
    
    todos = [
        {
            "content": "Implement user registration with email validation",
            "status": "pending",
            "priority": "high",
            "id": "user-reg-001"
        },
        {
            "content": "Create product catalog with search and filtering",
            "status": "pending",
            "priority": "high", 
            "id": "product-catalog-002"
        },
        {
            "content": "Build shopping cart with add/remove functionality",
            "status": "pending",
            "priority": "medium",
            "id": "shopping-cart-003"
        },
        {
            "content": "Implement checkout flow with payment integration",
            "status": "pending",
            "priority": "medium",
            "id": "checkout-flow-004"
        },
        {
            "content": "Add order history and tracking for users",
            "status": "pending",
            "priority": "low",
            "id": "order-history-005"
        }
    ]
    
    print("=== E-commerce Features Todo List ===")
    result = todo_write(todos)
    print(result)


def example_performance_optimization():
    """Example: Performance optimization with multiple identified issues."""
    
    todos = [
        {
            "content": "Implement memoization for expensive calculations in ProductList",
            "status": "in_progress",
            "priority": "high",
            "id": "perf-memo-001"
        },
        {
            "content": "Add virtualization for long lists in Dashboard",
            "status": "pending",
            "priority": "high",
            "id": "perf-virtual-002"
        },
        {
            "content": "Optimize image loading in Gallery component",
            "status": "pending",
            "priority": "medium",
            "id": "perf-images-003"
        },
        {
            "content": "Fix state update loops in ShoppingCart",
            "status": "pending",
            "priority": "high",
            "id": "perf-state-004"
        },
        {
            "content": "Review bundle size and implement code splitting",
            "status": "pending",
            "priority": "medium",
            "id": "perf-bundle-005"
        }
    ]
    
    print("=== Performance Optimization Todo List ===")
    result = todo_write(todos)
    print(result)


def example_warning_multiple_in_progress():
    """Example: Demonstrates warning when multiple tasks are in progress."""
    
    todos = [
        {
            "content": "Fix authentication bug in login component",
            "status": "in_progress",
            "priority": "high",
            "id": "auth-bug-001"
        },
        {
            "content": "Update API endpoints for user management",
            "status": "in_progress",  # This will trigger a warning
            "priority": "medium",
            "id": "api-update-002"
        },
        {
            "content": "Write unit tests for new features",
            "status": "pending",
            "priority": "low",
            "id": "unit-tests-003"
        }
    ]
    
    print("=== Multiple In-Progress Tasks (Warning Example) ===")
    result = todo_write(todos)
    print(result)


if __name__ == "__main__":
    print("TodoWrite LangChain Tool Examples\n")
    print("=" * 50)
    
    example_complex_task()
    print("\n" + "=" * 50)
    
    example_multiple_features()
    print("\n" + "=" * 50)
    
    example_performance_optimization()
    print("\n" + "=" * 50)
    
    example_warning_multiple_in_progress()
