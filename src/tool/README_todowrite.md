# TodoWrite LangChain Tool

这是一个用于创建和管理结构化任务列表的LangChain工具，帮助跟踪编程会话中的进度，组织复杂任务，并向用户展示工作的全面性。

## 功能特性

- 📋 结构化的任务列表管理
- 🔄 实时任务状态跟踪（pending, in_progress, completed）
- 🎯 任务优先级管理（high, medium, low）
- ⚠️ 多任务并行警告
- 📊 任务统计摘要
- 🎨 美观的格式化输出

## 何时使用此工具

### 应该使用的场景：

1. **复杂的多步骤任务** - 当任务需要3个或更多不同的步骤或操作时
2. **非平凡的复杂任务** - 需要仔细规划或多个操作的任务
3. **用户明确请求待办事项列表** - 当用户直接要求使用待办事项列表时
4. **用户提供多个任务** - 当用户提供要完成的事项列表时（编号或逗号分隔）
5. **收到新指令后** - 立即将用户需求捕获为待办事项
6. **开始处理任务时** - 在开始工作之前将其标记为in_progress
7. **完成任务后** - 将其标记为已完成并添加实施过程中发现的任何新后续任务

### 不应该使用的场景：

1. 只有一个简单直接的任务
2. 任务很简单，跟踪它不会带来组织上的好处
3. 任务可以在少于3个简单步骤中完成
4. 任务纯粹是对话性或信息性的

## 使用方法

### 基本用法

```python
from todowrite import todo_write

# 创建待办事项列表
todos = [
    {
        "content": "实现用户注册功能",
        "status": "pending",
        "priority": "high",
        "id": "user-reg-001"
    },
    {
        "content": "添加邮箱验证",
        "status": "pending", 
        "priority": "medium",
        "id": "email-validation-002"
    }
]

# 调用工具
result = todo_write(todos)
print(result)
```

### 任务状态管理

- **pending**: 尚未开始的任务
- **in_progress**: 当前正在处理的任务（建议一次只有一个）
- **completed**: 已成功完成的任务

### 优先级设置

- **high**: 高优先级（🔴）
- **medium**: 中等优先级（🟡）
- **low**: 低优先级（🟢）

## 输入格式

```python
{
    "todos": [
        {
            "content": "任务描述",
            "status": "pending|in_progress|completed",
            "priority": "high|medium|low", 
            "id": "唯一标识符"
        }
    ]
}
```

## 输出示例

```
📋 **Todo List** (3 items)

🔄 **In Progress:**
  🔴 实现用户注册功能 (ID: user-reg...)

⏳ **Pending:**
  🟡 添加邮箱验证 (ID: email-va...)
  🟢 编写单元测试 (ID: unit-tes...)

📊 **Summary:** 0 completed, 1 in progress, 2 pending
```

## 最佳实践

1. **一次只处理一个任务** - 避免多个任务同时处于in_progress状态
2. **及时更新状态** - 完成任务后立即标记为completed
3. **具体的任务描述** - 使用清晰、可操作的任务名称
4. **合理的任务分解** - 将复杂任务分解为较小的、可管理的步骤
5. **使用唯一ID** - 为每个任务提供唯一标识符以便跟踪

## 完整示例

查看 `todowrite_example.py` 文件以获取完整的使用示例，包括：

- 复杂多步骤任务管理
- 多功能实现跟踪
- 性能优化任务列表
- 多任务并行警告示例

## 依赖项

- `langchain_core`: LangChain核心库
- `pydantic`: 数据验证库
- `typing`: 类型提示支持
- `uuid`: 唯一标识符生成

## 注意事项

- 工具会自动生成缺失的ID和默认状态
- 当多个任务处于in_progress状态时会显示警告
- 输出按状态分组显示，优先显示进行中的任务
- 包含任务统计摘要以便快速了解进度
