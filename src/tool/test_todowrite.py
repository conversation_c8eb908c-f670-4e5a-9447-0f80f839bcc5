"""
Test file for TodoWrite LangChain tool.

Run this file to test the basic functionality of the TodoWrite tool.
"""

import sys
import os

# Add the current directory to the path so we can import todowrite
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from todowrite import todo_write
    print("✅ Successfully imported todo_write tool")
except ImportError as e:
    print(f"❌ Failed to import todo_write tool: {e}")
    sys.exit(1)


def test_basic_functionality():
    """Test basic todo list creation and formatting."""
    print("\n🧪 Testing basic functionality...")
    
    todos = [
        {
            "content": "测试任务1",
            "status": "pending",
            "priority": "high",
            "id": "test-001"
        },
        {
            "content": "测试任务2", 
            "status": "in_progress",
            "priority": "medium",
            "id": "test-002"
        },
        {
            "content": "测试任务3",
            "status": "completed",
            "priority": "low", 
            "id": "test-003"
        }
    ]
    
    try:
        result = todo_write(todos)
        print("✅ Basic functionality test passed")
        print("📋 Output:")
        print(result)
        return True
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False


def test_missing_fields():
    """Test handling of missing fields."""
    print("\n🧪 Testing missing fields handling...")
    
    todos = [
        {
            "content": "任务缺少ID",
            "status": "pending",
            "priority": "medium"
            # Missing 'id' field
        },
        {
            "content": "任务缺少状态",
            "priority": "high",
            "id": "test-missing-status"
            # Missing 'status' field
        },
        {
            "content": "任务缺少优先级",
            "status": "pending",
            "id": "test-missing-priority"
            # Missing 'priority' field
        }
    ]
    
    try:
        result = todo_write(todos)
        print("✅ Missing fields test passed")
        print("📋 Output:")
        print(result)
        return True
    except Exception as e:
        print(f"❌ Missing fields test failed: {e}")
        return False


def test_invalid_values():
    """Test handling of invalid field values."""
    print("\n🧪 Testing invalid values handling...")
    
    todos = [
        {
            "content": "无效状态任务",
            "status": "invalid_status",  # Invalid status
            "priority": "high",
            "id": "test-invalid-status"
        },
        {
            "content": "无效优先级任务",
            "status": "pending",
            "priority": "invalid_priority",  # Invalid priority
            "id": "test-invalid-priority"
        }
    ]
    
    try:
        result = todo_write(todos)
        print("✅ Invalid values test passed")
        print("📋 Output:")
        print(result)
        return True
    except Exception as e:
        print(f"❌ Invalid values test failed: {e}")
        return False


def test_multiple_in_progress():
    """Test warning for multiple in-progress tasks."""
    print("\n🧪 Testing multiple in-progress warning...")
    
    todos = [
        {
            "content": "进行中任务1",
            "status": "in_progress",
            "priority": "high",
            "id": "test-progress-1"
        },
        {
            "content": "进行中任务2",
            "status": "in_progress",  # This should trigger warning
            "priority": "medium",
            "id": "test-progress-2"
        }
    ]
    
    try:
        result = todo_write(todos)
        if "WARNING" in result:
            print("✅ Multiple in-progress warning test passed")
        else:
            print("⚠️ Warning not displayed as expected")
        print("📋 Output:")
        print(result)
        return True
    except Exception as e:
        print(f"❌ Multiple in-progress warning test failed: {e}")
        return False


def test_empty_list():
    """Test handling of empty todo list."""
    print("\n🧪 Testing empty todo list...")
    
    todos = []
    
    try:
        result = todo_write(todos)
        print("✅ Empty list test passed")
        print("📋 Output:")
        print(result)
        return True
    except Exception as e:
        print(f"❌ Empty list test failed: {e}")
        return False


def run_all_tests():
    """Run all tests and report results."""
    print("🚀 Starting TodoWrite tool tests...")
    print("=" * 50)
    
    tests = [
        test_basic_functionality,
        test_missing_fields,
        test_invalid_values,
        test_multiple_in_progress,
        test_empty_list
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print("-" * 30)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return True
    else:
        print("❌ Some tests failed.")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
