from typing import List, Dict, Any
from langchain_core.tools import tool
from pydantic import BaseModel, Field
import json
import uuid


class TodoItem(BaseModel):
    """A single todo item with content, status, priority, and unique ID."""
    content: str = Field(min_length=1, description="The todo item content")
    status: str = Field(description="Task status", pattern="^(pending|in_progress|completed)$")
    priority: str = Field(description="Task priority", pattern="^(high|medium|low)$")
    id: str = Field(description="Unique identifier for the todo item")


class TodoListInput(BaseModel):
    """Input schema for the TodoWrite tool."""
    todos: List[TodoItem] = Field(description="The updated todo list")


@tool(args_schema=TodoListInput)
def todo_write(todos: List[Dict[str, Any]]) -> str:
    """
    Use this tool to create and manage a structured task list for your current
    coding session. This helps you track progress, organize complex tasks, and
    demonstrate thoroughness to the user.
    It also helps the user understand the progress of the task and overall
    progress of their requests.

    ## When to Use This Tool
    Use this tool proactively in these scenarios:

    1. Complex multi-step tasks - When a task requires 3 or more distinct steps
       or actions
    2. Non-trivial and complex tasks - Tasks that require careful planning or
       multiple operations
    3. User explicitly requests todo list - When the user directly asks you to
       use the todo list
    4. User provides multiple tasks - When users provide a list of things to be
       done (numbered or comma-separated)
    5. After receiving new instructions - Immediately capture user requirements
       as todos
    6. When you start working on a task - Mark it as in_progress BEFORE beginning
       work. Ideally you should only have one todo as in_progress at a time
    7. After completing a task - Mark it as completed and add any new follow-up
       tasks discovered during implementation

    ## When NOT to Use This Tool

    Skip using this tool when:
    1. There is only a single, straightforward task
    2. The task is trivial and tracking it provides no organizational benefit
    3. The task can be completed in less than 3 trivial steps
    4. The task is purely conversational or informational

    NOTE that you should not use this tool if there is only one trivial task to
    do. In this case you are better off just doing the task directly.

    ## Task States and Management

    1. **Task States**: Use these states to track progress:
       - pending: Task not yet started
       - in_progress: Currently working on (limit to ONE task at a time)
       - completed: Task finished successfully

    2. **Task Management**:
       - Update task status in real-time as you work
       - Mark tasks complete IMMEDIATELY after finishing (don't batch
         completions)
       - Only have ONE task in_progress at any time
       - Complete current tasks before starting new ones
       - Remove tasks that are no longer relevant from the list entirely

    3. **Task Completion Requirements**:
       - ONLY mark a task as completed when you have FULLY accomplished it
       - If you encounter errors, blockers, or cannot finish, keep the task as
         in_progress
       - When blocked, create a new task describing what needs to be resolved
       - Never mark a task as completed if:
         - Tests are failing
         - Implementation is partial
         - You encountered unresolved errors
         - You couldn't find necessary files or dependencies

    4. **Task Breakdown**:
       - Create specific, actionable items
       - Break complex tasks into smaller, manageable steps
       - Use clear, descriptive task names

    When in doubt, use this tool. Being proactive with task management
    demonstrates attentiveness and ensures you complete all requirements
    successfully.

    Args:
        todos: List of todo items with content, status, priority, and id

    Returns:
        A formatted string representation of the todo list
    """

    # Validate and process todos
    processed_todos = []
    in_progress_count = 0

    for todo_dict in todos:
        # Ensure all required fields are present
        if not all(key in todo_dict for key in ['content', 'status', 'priority', 'id']):
            # Generate missing fields if needed
            if 'id' not in todo_dict:
                todo_dict['id'] = str(uuid.uuid4())
            if 'status' not in todo_dict:
                todo_dict['status'] = 'pending'
            if 'priority' not in todo_dict:
                todo_dict['priority'] = 'medium'

        # Validate status
        if todo_dict['status'] not in ['pending', 'in_progress', 'completed']:
            todo_dict['status'] = 'pending'

        # Validate priority
        if todo_dict['priority'] not in ['high', 'medium', 'low']:
            todo_dict['priority'] = 'medium'

        # Count in_progress tasks
        if todo_dict['status'] == 'in_progress':
            in_progress_count += 1

        processed_todos.append(todo_dict)

    # Warn if multiple tasks are in progress
    warning = ""
    if in_progress_count > 1:
        warning = "⚠️  WARNING: Multiple tasks are marked as in_progress. Consider focusing on one task at a time.\n\n"

    # Format the todo list for display
    result = f"{warning}📋 **Todo List** ({len(processed_todos)} items)\n\n"

    # Group by status for better organization
    pending_todos = [t for t in processed_todos if t['status'] == 'pending']
    in_progress_todos = [t for t in processed_todos if t['status'] == 'in_progress']
    completed_todos = [t for t in processed_todos if t['status'] == 'completed']

    # Display in_progress first (most important)
    if in_progress_todos:
        result += "🔄 **In Progress:**\n"
        for todo in in_progress_todos:
            priority_emoji = {"high": "🔴", "medium": "🟡", "low": "🟢"}[todo['priority']]
            result += f"  {priority_emoji} {todo['content']} (ID: {todo['id'][:8]}...)\n"
        result += "\n"

    # Then pending tasks
    if pending_todos:
        result += "⏳ **Pending:**\n"
        for todo in pending_todos:
            priority_emoji = {"high": "🔴", "medium": "🟡", "low": "🟢"}[todo['priority']]
            result += f"  {priority_emoji} {todo['content']} (ID: {todo['id'][:8]}...)\n"
        result += "\n"

    # Finally completed tasks
    if completed_todos:
        result += "✅ **Completed:**\n"
        for todo in completed_todos:
            priority_emoji = {"high": "🔴", "medium": "🟡", "low": "🟢"}[todo['priority']]
            result += f"  {priority_emoji} {todo['content']} (ID: {todo['id'][:8]}...)\n"
        result += "\n"

    # Add summary statistics
    result += f"📊 **Summary:** {len(completed_todos)} completed, {len(in_progress_todos)} in progress, {len(pending_todos)} pending"

    return result