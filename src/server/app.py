import logging
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware

from typing import Optional

logger = logging.getLogger(__name__)


app = FastAPI(
    title="OnePort Agent API",
    description="API for OnePort Agent",
    version="0.0.1",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

ttl_config = {
    #ttl统一设置为3天
    "default_ttl": 4320,
    "refresh_on_read": True
}



