import argparse
import logging
import os
from logging.handlers import RotatingFileHandler
import uvicorn

# 创建logs目录（如果不存在）
os.makedirs('logs', exist_ok=True)

# 配置日志
log_file_path = 'logs/oneport_agent.log'
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s:%(lineno)d - %(levelname)s - %(message)s",
    handlers=[
        # 输出到控制台
        logging.StreamHandler(),
        # 输出到文件，最大10MB，保留5个备份
        RotatingFileHandler(log_file_path, maxBytes=10*1024*1024, backupCount=5)
    ]
)

logger = logging.getLogger(__name__)
logger.info(f"Logs will be written to: {os.path.abspath(log_file_path)}")

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Run the OnePort Agent API server")
    parser.add_argument(
        "--host",
        type=str,
        default="localhost",
        help="Host to bind the server to (default: localhost)",
    )
    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="Port to bind the server to (default: 8000)",
    )
    parser.add_argument(
        "--log-level",
        type=str,
        default="info",
        choices=["debug", "info", "warning", "error", "critical"],
        help="Log level (default: info)",
    )

    args = parser.parse_args()
    

    logger.info("Starting OnePort Agent API server")
    uvicorn.run(
        "src.server.app:app",
        host=args.host,
        port=args.port,
        log_level=args.log_level,
    )
